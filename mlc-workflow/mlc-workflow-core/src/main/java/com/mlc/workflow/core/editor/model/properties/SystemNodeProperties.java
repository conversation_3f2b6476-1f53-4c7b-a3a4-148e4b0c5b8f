package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统任务节点
 * 对应NodeTypeEnum.SYSTEM (100)
 */
@Getter
@Setter
public class SystemNodeProperties extends BaseNodeProperties {

    public SystemNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.SYSTEM.getValue());
        this.setName("系统任务");
    }
}
