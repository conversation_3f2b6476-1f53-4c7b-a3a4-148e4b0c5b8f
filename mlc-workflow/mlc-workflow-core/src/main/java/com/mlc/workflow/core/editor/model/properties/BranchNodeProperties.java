package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 分支节点(typeId=1)
 * 用于流程分支
 */
@Getter
@Setter
public class BranchNodeProperties extends BaseNodeProperties {

    public BranchNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.BRANCH.getValue());
        this.setName("分支任务");
    }

    /**
     * 操作条件（表达式）
     */
    private String operateCondition;

}
