package com.mlc.workflow.core.editor.structure.traverse;

import com.mlc.workflow.core.editor.model.canvas.ApprovalNode;
import com.mlc.workflow.core.editor.model.canvas.ApprovalProcessNode;
import com.mlc.workflow.core.editor.model.canvas.CcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GetMoreRecordNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.NotifyNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SubProcessNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SystemNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WriteNode;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;

/**
 * 节点访问者接口
 * 实现访问者模式，用于统一遍历不同类型的节点
 */
public interface NodeVisitor {
    
    /**
     * 访问开始事件节点
     * @param node 开始事件节点
     * @param context 遍历上下文
     */
    void visitStartEvent(StartEventNodeCanvas node, TraverseContext context);
    
    /**
     * 访问网关节点
     * @param node 网关节点
     * @param context 遍历上下文
     */
    void visitGateway(GatewayNodeCanvas node, TraverseContext context);
    
    /**
     * 访问条件节点（分支叶子）
     * @param node 条件节点
     * @param context 遍历上下文
     */
    void visitCondition(ConditionNodeCanvas node, TraverseContext context);
    
    /**
     * 访问审批节点
     * @param node 审批节点
     * @param context 遍历上下文
     */
    void visitApproval(ApprovalNode node, TraverseContext context);
    
    /**
     * 访问审批流程节点（包含子流程）
     * @param node 审批流程节点
     * @param context 遍历上下文
     */
    void visitApprovalProcess(ApprovalProcessNode node, TraverseContext context);
    
    /**
     * 访问填写节点
     * @param node 填写节点
     * @param context 遍历上下文
     */
    void visitWrite(WriteNode node, TraverseContext context);
    
    /**
     * 访问抄送节点
     * @param node 抄送节点
     * @param context 遍历上下文
     */
    void visitCc(CcNodeCanvas node, TraverseContext context);
    
    /**
     * 访问通知节点
     * @param node 通知节点
     * @param context 遍历上下文
     */
    void visitNotify(NotifyNodeCanvas node, TraverseContext context);
    
    /**
     * 访问获取更多记录节点
     * @param node 获取更多记录节点
     * @param context 遍历上下文
     */
    void visitGetMoreRecord(GetMoreRecordNodeCanvas node, TraverseContext context);
    
    /**
     * 访问子流程节点
     * @param node 子流程节点
     * @param context 遍历上下文
     */
    void visitSubProcess(SubProcessNodeCanvas node, TraverseContext context);
    
    /**
     * 访问系统节点
     * @param node 系统节点
     * @param context 遍历上下文
     */
    void visitSystem(SystemNodeCanvas node, TraverseContext context);
    
    /**
     * 访问流程节点（通用方法）
     * @param node 流程节点
     * @param context 遍历上下文
     */
    default void visitFlowNode(FlowNodeCanvas node, TraverseContext context) {
        // 默认实现，可以在具体访问者中重写
    }
    
    /**
     * 开始访问流程
     * @param processNode 流程节点
     * @param context 遍历上下文
     */
    default void startVisit(ProcessNode processNode, TraverseContext context) {
        // 默认实现，可以在具体访问者中重写
    }
    
    /**
     * 结束访问流程
     * @param processNode 流程节点
     * @param context 遍历上下文
     */
    default void endVisit(ProcessNode processNode, TraverseContext context) {
        // 默认实现，可以在具体访问者中重写
    }
}
