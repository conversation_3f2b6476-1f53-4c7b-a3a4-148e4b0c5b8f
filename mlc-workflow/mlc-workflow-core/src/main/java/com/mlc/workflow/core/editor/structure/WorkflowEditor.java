package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.operation.BranchOperations;
import com.mlc.workflow.core.editor.structure.operation.GatewayOperations;
import com.mlc.workflow.core.editor.structure.operation.NodeOperations;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 工作流编辑器
 * 对外服务接口，整合所有操作命令和校验器，提供统一的编辑入口
 */
@Slf4j
public class WorkflowEditor {
    
    private final GatewayOperations gatewayOperations;
    private final BranchOperations branchOperations;
    private final NodeOperations nodeOperations;
    private final NodeBatchExecutor nodeBatchExecutor;
    private final WorkflowValidator validator;

    public WorkflowEditor(GatewayOperations gatewayOperations, BranchOperations branchOperations,
                          NodeOperations nodeOperations, NodeBatchExecutor nodeBatchExecutor,
                          WorkflowValidator validator) {
        this.gatewayOperations = gatewayOperations;
        this.branchOperations = branchOperations;
        this.nodeOperations = nodeOperations;
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.validator = validator;
    }

    // ==================== 网关操作 ====================
    
    /**
     * 新增网关
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(ProcessNode processNode, String atNodeId,
                                GatewayOperations.PlacementStrategy placement) {
        return executeWithTransaction(processNode,
            () -> gatewayOperations.addGateway(atNodeId, placement),
            "新增网关");
    }

    /**
     * 删除网关
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     */
    public void deleteGateway(ProcessNode processNode, String gatewayId) {
        executeWithTransaction(processNode,
            () -> gatewayOperations.deleteGateway(gatewayId),
            "删除网关", gatewayId);
    }

    /**
     * 切换网关类型
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(ProcessNode processNode, String gatewayId, Integer toType) {
        executeWithTransaction(processNode,
            () -> gatewayOperations.switchGatewayType(gatewayId, toType),
            "切换网关类型", gatewayId + " -> " + toType);
    }

    // ==================== 分支操作 ====================
    
    /**
     * 新增分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param position 插入位置
     * @return 创建的分支叶子节点
     */
    public ConditionNodeCanvas addBranch(ProcessNode processNode, String gatewayId, int position) {
        return executeWithTransaction(processNode,
            () -> branchOperations.addBranch(gatewayId, position),
            "新增分支");
    }

    /**
     * 删除分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(ProcessNode processNode, String gatewayId, String branchLeafId) {
        executeWithTransaction(processNode,
            () -> branchOperations.deleteBranch(gatewayId, branchLeafId),
            "删除分支", branchLeafId);
    }
    
    /**
     * 调整分支顺序
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(ProcessNode processNode, String gatewayId, List<String> newOrder) {
        executeWithTransaction(processNode,
            () -> branchOperations.reorderBranches(gatewayId, newOrder),
            "调整分支顺序", gatewayId);
    }

    /**
     * 复制分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置
     * @return 复制的分支叶子节点
     */
    public ConditionNodeCanvas duplicateBranch(ProcessNode processNode, String gatewayId,
                                       String branchLeafId, int position) {
        return executeWithTransaction(processNode,
            () -> branchOperations.duplicateBranch(gatewayId, branchLeafId, position),
            "复制分支");
    }

    // ==================== 普通节点操作 ====================
    
    /**
     * 插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param insertNode 新节点规格
     * @return 创建的节点
     */
    public BaseNodeCanvas insertNode(ProcessNode processNode, String afterNodeId, BaseNodeCanvas insertNode) {
        return executeWithTransaction(processNode,
            () -> nodeOperations.insertNode(afterNodeId, insertNode),
            "插入节点");
    }

    /**
     * 删除节点
     * @param processNode 流程节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(ProcessNode processNode, String nodeId) {
        executeWithTransaction(processNode,
            () -> nodeOperations.deleteNode(nodeId),
            "删除节点", nodeId);
    }

    /**
     * 更新节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(ProcessNode processNode, String nodeId, Map<String, Object> updates) {
        executeWithTransaction(processNode,
            () -> nodeOperations.updateNode(nodeId, updates),
            "更新节点", nodeId);
    }

    // ==================== 验证操作 ====================
    
    /**
     * 验证流程
     * @param processNode 流程节点
     * @return 验证结果
     */
    public WorkflowValidator.ValidationResult validate(ProcessNode processNode) {
        return validator.validate(processNode);
    }
    

    // ==================== 辅助方法 ====================
    /**
     * 执行带事务的操作，有返回值
     * @param processNode 流程节点
     * @param operation 具体操作
     * @param operationName 操作名称，用于日志
     * @return 操作结果
     */
    private <T> T executeWithTransaction(ProcessNode processNode, Supplier<T> operation, String operationName) {
        nodeBatchExecutor.begin(processNode);
        try {
            T result = operation.get();

            if (nodeBatchExecutor.commit()) {
                log.info("成功{}: {}", operationName, result);
                return result;
            } else {
                throw new RuntimeException(operationName + "失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 执行带事务的操作，无返回值
     * @param processNode 流程节点
     * @param operation 具体操作
     * @param operationName 操作名称，用于日志
     * @param logParam 日志参数
     */
    private void executeWithTransaction(ProcessNode processNode, Runnable operation, String operationName, Object logParam) {
        nodeBatchExecutor.begin(processNode);
        try {
            operation.run();

            if (nodeBatchExecutor.commit()) {
                log.info("成功{}: {}", operationName, logParam);
            } else {
                throw new RuntimeException(operationName + "失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

}
