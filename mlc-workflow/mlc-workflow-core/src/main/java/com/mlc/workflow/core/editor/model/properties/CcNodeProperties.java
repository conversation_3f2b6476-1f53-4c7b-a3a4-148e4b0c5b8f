package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 抄送任务节点
 * 对应NodeTypeEnum.CC (5)
 */
@Getter
@Setter
public class CcNodeProperties extends BaseNodeProperties {

    public CcNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.CC.getValue());
        this.setName("抄送任务");
    }
}
