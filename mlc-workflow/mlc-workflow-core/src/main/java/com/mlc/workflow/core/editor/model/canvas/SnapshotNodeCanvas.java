package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取页面快照任务节点
 * 对应NodeTypeEnum.SNAPSHOT (28)
 */
@Getter
@Setter
@DataBean
public class SnapshotNodeCanvas extends BaseNodeCanvas {

    public SnapshotNodeCanvas() {
        this.setTypeId(NodeTypeEnum.SNAPSHOT.getValue());
        this.setName("获取页面快照任务");
    }
}
