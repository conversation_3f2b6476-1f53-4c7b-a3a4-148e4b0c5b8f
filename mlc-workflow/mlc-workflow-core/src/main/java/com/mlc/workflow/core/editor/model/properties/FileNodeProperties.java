package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取记录打印文件节点
 * 对应NodeTypeEnum.FILE (18)
 */
@Getter
@Setter
public class FileNodeProperties extends BaseNodeProperties {

    public FileNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.FILE.getValue());
        this.setName("获取记录打印文件");
    }
}
