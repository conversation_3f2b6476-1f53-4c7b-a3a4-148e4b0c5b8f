package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 填写节点 (typeId=3)
 * 用于表单填写任务
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class WriteNode extends FlowNodeCanvas implements IHasAccounts {
    
    /**
     * 账户列表
     */
    private List<Account> accounts;
    
    /**
     * 表单属性列表
     */
    private List<Object> formProperties;

    public WriteNode() {
        this.setName("填写节点");
        this.setTypeId(NodeTypeEnum.WRITE.getValue());
    }

    /**
     * 验证填写节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && hasValidAccounts();
    }
}
