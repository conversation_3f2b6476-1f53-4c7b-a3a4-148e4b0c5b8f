package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 循环任务节点
 * 对应NodeTypeEnum.LOOP (29)
 */
@Getter
@Setter
public class LoopNodeProperties extends BaseNodeProperties {

    public LoopNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.LOOP.getValue());
        this.setName("循环任务");
    }
}
