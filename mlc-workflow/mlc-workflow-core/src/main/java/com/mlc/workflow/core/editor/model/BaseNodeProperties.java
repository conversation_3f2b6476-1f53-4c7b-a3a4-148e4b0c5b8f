package com.mlc.workflow.core.editor.model;

import static com.mlc.base.common.enums.workflow.NodeTypeEnum.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.properties.ActionNodeProperties;
import com.mlc.workflow.core.editor.model.properties.AigcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApiNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApiPackageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApprovalNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ApprovalProcessNodeProperties;
import com.mlc.workflow.core.editor.model.properties.AuthenticationNodeProperties;
import com.mlc.workflow.core.editor.model.properties.BranchNodeProperties;
import com.mlc.workflow.core.editor.model.properties.CcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.CodeNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ConditionNodeProperties;
import com.mlc.workflow.core.editor.model.properties.DelayNodeProperties;
import com.mlc.workflow.core.editor.model.properties.EmailNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FileNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FindMoreMessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FindSingleMessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.FormulaNodeProperties;
import com.mlc.workflow.core.editor.model.properties.GetMoreRecordNodeProperties;
import com.mlc.workflow.core.editor.model.properties.JsonParseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.LinkNodeProperties;
import com.mlc.workflow.core.editor.model.properties.LoopNodeProperties;
import com.mlc.workflow.core.editor.model.properties.MessageNodeProperties;
import com.mlc.workflow.core.editor.model.properties.NotifyNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ParameterNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PbcNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PluginNodeProperties;
import com.mlc.workflow.core.editor.model.properties.PushNodeProperties;
import com.mlc.workflow.core.editor.model.properties.ReturnNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SearchNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SnapshotNodeProperties;
import com.mlc.workflow.core.editor.model.properties.StartEventNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SubProcessNodeProperties;
import com.mlc.workflow.core.editor.model.properties.SystemNodeProperties;
import com.mlc.workflow.core.editor.model.properties.TemplateNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WebhookNodeProperties;
import com.mlc.workflow.core.editor.model.properties.WriteNodeProperties;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 节点基类
 * 包含所有节点的公共属性
 */
@Getter
@Setter
@DataBean
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "flowNodeType", visible = true)
@JsonSubTypes({
    @JsonSubTypes.Type(value = StartEventNodeProperties.class, name = TYPE_START),
    @JsonSubTypes.Type(value = BranchNodeProperties.class, name = TYPE_BRANCH),
    @JsonSubTypes.Type(value = ConditionNodeProperties.class, name = TYPE_BRANCH_ITEM),
    @JsonSubTypes.Type(value = WriteNodeProperties.class, name = TYPE_WRITE),
    @JsonSubTypes.Type(value = ApprovalNodeProperties.class, name = TYPE_APPROVAL),
    @JsonSubTypes.Type(value = CcNodeProperties.class, name = TYPE_CC),
    @JsonSubTypes.Type(value = ActionNodeProperties.class, name = TYPE_ACTION),
    @JsonSubTypes.Type(value = SearchNodeProperties.class, name = TYPE_SEARCH),
    @JsonSubTypes.Type(value = WebhookNodeProperties.class, name = TYPE_WEBHOOK),
    @JsonSubTypes.Type(value = FormulaNodeProperties.class, name = TYPE_FORMULA),
    @JsonSubTypes.Type(value = MessageNodeProperties.class, name = TYPE_MESSAGE),
    @JsonSubTypes.Type(value = EmailNodeProperties.class, name = TYPE_EMAIL),
    @JsonSubTypes.Type(value = DelayNodeProperties.class, name = TYPE_DELAY),
    @JsonSubTypes.Type(value = GetMoreRecordNodeProperties.class, name = TYPE_GET_MORE_RECORD),
    @JsonSubTypes.Type(value = CodeNodeProperties.class, name = TYPE_CODE),
    @JsonSubTypes.Type(value = LinkNodeProperties.class, name = TYPE_LINK),
    @JsonSubTypes.Type(value = SubProcessNodeProperties.class, name = TYPE_SUB_PROCESS),
    @JsonSubTypes.Type(value = PushNodeProperties.class, name = TYPE_PUSH),
    @JsonSubTypes.Type(value = FileNodeProperties.class, name = TYPE_FILE),
    @JsonSubTypes.Type(value = TemplateNodeProperties.class, name = TYPE_TEMPLATE),
    @JsonSubTypes.Type(value = PbcNodeProperties.class, name = TYPE_PBC),
    @JsonSubTypes.Type(value = JsonParseNodeProperties.class, name = TYPE_JSON_PARSE),
    @JsonSubTypes.Type(value = AuthenticationNodeProperties.class, name = TYPE_AUTHENTICATION),
    @JsonSubTypes.Type(value = ParameterNodeProperties.class, name = TYPE_PARAMETER),
    @JsonSubTypes.Type(value = ApiPackageNodeProperties.class, name = TYPE_API_PACKAGE),
    @JsonSubTypes.Type(value = ApiNodeProperties.class, name = TYPE_API),
    @JsonSubTypes.Type(value = ApprovalProcessNodeProperties.class, name = TYPE_APPROVAL_PROCESS),
    @JsonSubTypes.Type(value = NotifyNodeProperties.class, name = TYPE_NOTICE),
    @JsonSubTypes.Type(value = SnapshotNodeProperties.class, name = TYPE_SNAPSHOT),
    @JsonSubTypes.Type(value = LoopNodeProperties.class, name = TYPE_LOOP),
    @JsonSubTypes.Type(value = ReturnNodeProperties.class, name = TYPE_RETURN),
    @JsonSubTypes.Type(value = AigcNodeProperties.class, name = TYPE_AIGC),
    @JsonSubTypes.Type(value = PluginNodeProperties.class, name = TYPE_PLUGIN),
    @JsonSubTypes.Type(value = SystemNodeProperties.class, name = TYPE_SYSTEM),
    @JsonSubTypes.Type(value = FindSingleMessageNodeProperties.class, name = TYPE_FIND_SINGLE_MESSAGE),
    @JsonSubTypes.Type(value = FindMoreMessageNodeProperties.class, name = TYPE_FIND_MORE_MESSAGE)
})
public abstract class BaseNodeProperties {

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 节点ID
     */
    private String nodeId;

    /**
     * 流程节点类型
     */
    private Integer flowNodeType;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 选择节点ID
     */
    private String selectNodeId;

    /**
     * 获取节点类型枚举
     */
    @JsonIgnore
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.fromValue(this.flowNodeType);
    }
    
    /**
     * 验证节点基础信息
     */
    @JsonIgnore
    public boolean isValid() {
        return flowNodeType != null && name != null;
    }
}
