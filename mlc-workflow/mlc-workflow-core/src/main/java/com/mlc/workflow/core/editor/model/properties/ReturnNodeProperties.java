package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 强制中止任务节点
 * 对应NodeTypeEnum.RETURN (30)
 */
@Getter
@Setter
public class ReturnNodeProperties extends BaseNodeProperties {

    public ReturnNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.RETURN.getValue());
        this.setName("强制中止任务");
    }
}
