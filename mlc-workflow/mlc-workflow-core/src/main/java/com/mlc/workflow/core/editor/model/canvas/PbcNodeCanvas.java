package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 调用封装业务流程任务节点
 * 对应NodeTypeEnum.PBC (20)
 */
@Getter
@Setter
@DataBean
public class PbcNodeCanvas extends BaseNodeCanvas {

    public PbcNodeCanvas() {
        this.setTypeId(NodeTypeEnum.PBC.getValue());
        this.setName("调用封装业务流程任务");
    }
}
