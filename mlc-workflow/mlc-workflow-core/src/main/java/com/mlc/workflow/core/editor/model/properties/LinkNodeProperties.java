package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取链接节点
 * 对应NodeTypeEnum.LINK (15)
 */
@Getter
@Setter
public class LinkNodeProperties extends BaseNodeProperties {

    public LinkNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.LINK.getValue());
        this.setName("获取链接");
    }
}
