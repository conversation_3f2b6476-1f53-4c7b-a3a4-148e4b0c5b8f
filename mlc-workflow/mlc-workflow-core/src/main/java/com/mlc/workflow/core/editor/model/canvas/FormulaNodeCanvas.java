package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 公式任务节点
 * 对应NodeTypeEnum.FORMULA (9)
 */
@Getter
@Setter
@DataBean
public class FormulaNodeCanvas extends FlowNodeCanvas {

    private String actionId;

    private String formulaValue;

    private Object formulaMap;

    private String fieldValue;

    public FormulaNodeCanvas() {
        this.setTypeId(NodeTypeEnum.FORMULA.getValue());
        this.setName("公式任务");
    }
}
