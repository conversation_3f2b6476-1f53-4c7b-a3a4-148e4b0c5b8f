package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 服务号消息任务节点
 * 对应NodeTypeEnum.TEMPLATE (19)
 */
@Getter
@Setter
public class TemplateNodeProperties extends BaseNodeProperties {

    public TemplateNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.TEMPLATE.getValue());
        this.setName("服务号消息任务");
    }
}
