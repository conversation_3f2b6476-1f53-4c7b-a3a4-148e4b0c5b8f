package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 动作任务节点
 * 对应NodeTypeEnum.ACTION (6)
 */
@Getter
@Setter
public class ActionNodeProperties extends BaseNodeProperties {

    public ActionNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.ACTION.getValue());
        this.setName("动作任务");
    }
}
