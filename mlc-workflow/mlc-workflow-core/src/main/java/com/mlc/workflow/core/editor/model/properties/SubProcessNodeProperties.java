package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 子流程节点 (typeId=16)
 * 用于嵌套子流程
 */
@Getter
@Setter
public class SubProcessNodeProperties extends BaseNodeProperties {

    public SubProcessNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.SUB_PROCESS.getValue());
        this.setName("子流程任务");
    }

    /**
     * 执行方式 0-按条件执行 1-执行所有
     */
    private Integer executeType;

    /**
     * 子流程ID
     */
    private String subProcessId;
}
