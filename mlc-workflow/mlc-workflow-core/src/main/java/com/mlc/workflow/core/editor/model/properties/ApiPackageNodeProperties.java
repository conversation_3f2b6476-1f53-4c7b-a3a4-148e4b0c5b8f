package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.API_PACKAGE (24)
 */
@Getter
@Setter
public class ApiPackageNodeProperties extends BaseNodeProperties {

    public ApiPackageNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.API_PACKAGE.getValue());
        this.setName("API 连接与认证任务");
    }
}
