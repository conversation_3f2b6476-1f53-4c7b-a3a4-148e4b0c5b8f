package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 通知节点 (typeId=27)
 * 用于发送站内通知
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class NotifyNodeCanvas extends FlowNodeCanvas implements IHasAccounts {
    
    /**
     * 账户列表
     */
    private List<Account> accounts;

    public NotifyNodeCanvas() {
        this.setName("通知节点");
        this.setTypeId(NodeTypeEnum.NOTICE.getValue());
    }

    /**
     * 验证通知节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && hasValidAccounts();
    }
}
