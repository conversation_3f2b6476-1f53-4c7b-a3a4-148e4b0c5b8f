package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 发送 API 请求任务节点
 * 对应NodeTypeEnum.WEBHOOK (8)
 */
@Getter
@Setter
public class WebhookNodeProperties extends BaseNodeProperties {

    public WebhookNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.WEBHOOK.getValue());
        this.setName("发送 API 请求任务");
    }
}
