package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取链接节点
 * 对应NodeTypeEnum.LINK (15)
 */
@Getter
@Setter
@DataBean
public class LinkNodeCanvas extends BaseNodeCanvas {

    public LinkNodeCanvas() {
        this.setTypeId(NodeTypeEnum.LINK.getValue());
        this.setName("获取链接");
    }
}
