package com.mlc.workflow.core.editor.model;

import static com.mlc.base.common.enums.workflow.NodeTypeEnum.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.canvas.ActionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.AigcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ApiNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ApiPackageNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ApprovalNode;
import com.mlc.workflow.core.editor.model.canvas.ApprovalProcessNode;
import com.mlc.workflow.core.editor.model.canvas.AuthenticationNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.CcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.CodeNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.DelayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.EmailNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FileNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FindMoreMessageNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FindSingleMessageNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FormulaNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GetMoreRecordNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.JsonParseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.LinkNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.LoopNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.MessageNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.NotifyNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ParameterNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.PbcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.PluginNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.PushNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ReturnNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SearchNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SnapshotNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SubProcessNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SystemNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.TemplateNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WebhookNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WriteNode;
import io.nop.api.core.annotations.data.DataBean;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

/**
 * 节点基础抽象类
 * 包含所有节点类型的通用字段
 */
@Getter
@Setter
@DataBean
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "typeId", visible = true)
@JsonSubTypes({
    @JsonSubTypes.Type(value = StartEventNodeCanvas.class, name = TYPE_START),
    @JsonSubTypes.Type(value = GatewayNodeCanvas.class, name = TYPE_BRANCH),
    @JsonSubTypes.Type(value = ConditionNodeCanvas.class, name = TYPE_BRANCH_ITEM),
    @JsonSubTypes.Type(value = WriteNode.class, name = TYPE_WRITE),
    @JsonSubTypes.Type(value = ApprovalNode.class, name = TYPE_APPROVAL),
    @JsonSubTypes.Type(value = CcNodeCanvas.class, name = TYPE_CC),
    @JsonSubTypes.Type(value = ActionNodeCanvas.class, name = TYPE_ACTION),
    @JsonSubTypes.Type(value = SearchNodeCanvas.class, name = TYPE_SEARCH),
    @JsonSubTypes.Type(value = WebhookNodeCanvas.class, name = TYPE_WEBHOOK),
    @JsonSubTypes.Type(value = FormulaNodeCanvas.class, name = TYPE_FORMULA),
    @JsonSubTypes.Type(value = MessageNodeCanvas.class, name = TYPE_MESSAGE),
    @JsonSubTypes.Type(value = EmailNodeCanvas.class, name = TYPE_EMAIL),
    @JsonSubTypes.Type(value = DelayNodeCanvas.class, name = TYPE_DELAY),
    @JsonSubTypes.Type(value = GetMoreRecordNodeCanvas.class, name = TYPE_GET_MORE_RECORD),
    @JsonSubTypes.Type(value = CodeNodeCanvas.class, name = TYPE_CODE),
    @JsonSubTypes.Type(value = LinkNodeCanvas.class, name = TYPE_LINK),
    @JsonSubTypes.Type(value = SubProcessNodeCanvas.class, name = TYPE_SUB_PROCESS),
    @JsonSubTypes.Type(value = PushNodeCanvas.class, name = TYPE_PUSH),
    @JsonSubTypes.Type(value = FileNodeCanvas.class, name = TYPE_FILE),
    @JsonSubTypes.Type(value = TemplateNodeCanvas.class, name = TYPE_TEMPLATE),
    @JsonSubTypes.Type(value = PbcNodeCanvas.class, name = TYPE_PBC),
    @JsonSubTypes.Type(value = JsonParseNodeCanvas.class, name = TYPE_JSON_PARSE),
    @JsonSubTypes.Type(value = AuthenticationNodeCanvas.class, name = TYPE_AUTHENTICATION),
    @JsonSubTypes.Type(value = ParameterNodeCanvas.class, name = TYPE_PARAMETER),
    @JsonSubTypes.Type(value = ApiPackageNodeCanvas.class, name = TYPE_API_PACKAGE),
    @JsonSubTypes.Type(value = ApiNodeCanvas.class, name = TYPE_API),
    @JsonSubTypes.Type(value = ApprovalProcessNode.class, name = TYPE_APPROVAL_PROCESS),
    @JsonSubTypes.Type(value = NotifyNodeCanvas.class, name = TYPE_NOTICE),
    @JsonSubTypes.Type(value = SnapshotNodeCanvas.class, name = TYPE_SNAPSHOT),
    @JsonSubTypes.Type(value = LoopNodeCanvas.class, name = TYPE_LOOP),
    @JsonSubTypes.Type(value = ReturnNodeCanvas.class, name = TYPE_RETURN),
    @JsonSubTypes.Type(value = AigcNodeCanvas.class, name = TYPE_AIGC),
    @JsonSubTypes.Type(value = PluginNodeCanvas.class, name = TYPE_PLUGIN),
    @JsonSubTypes.Type(value = SystemNodeCanvas.class, name = TYPE_SYSTEM),
    @JsonSubTypes.Type(value = FindSingleMessageNodeCanvas.class, name = TYPE_FIND_SINGLE_MESSAGE),
    @JsonSubTypes.Type(value = FindMoreMessageNodeCanvas.class, name = TYPE_FIND_MORE_MESSAGE)
})
public abstract class BaseNodeCanvas {

    /**
     * 节点ID
     */
    private String id = UUID.randomUUID().toString().replace("-", "");
    
    /**
     * 节点类型ID
     */
    @JsonIgnore
    private Integer typeId;
    
    /**
     * 节点名称
     */
    private String name;
    
    /**
     * 节点描述
     */
    private String desc = "";
    
    /**
     * 节点别名
     */
    private String alias = "";

    /**
     * 获取节点类型枚举
     */
    @JsonIgnore
    public NodeTypeEnum getNodeType() {
        return fromValue(this.typeId);
    }
    
    /**
     * 验证节点基础信息
     */
    @JsonIgnore
    public boolean isValid() {
        return typeId != null && name != null;
    }
}
