package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.API_PACKAGE (24)
 */
@Getter
@Setter
@DataBean
public class ApiPackageNodeCanvas extends BaseNodeCanvas {

    public ApiPackageNodeCanvas() {
        this.setTypeId(NodeTypeEnum.API_PACKAGE.getValue());
        this.setName("API 连接与认证任务");
    }
}
