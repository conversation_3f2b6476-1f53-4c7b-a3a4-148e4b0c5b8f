package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 连接参数任务节点
 * 对应NodeTypeEnum.PARAMETER (23)
 */
@Getter
@Setter
@DataBean
public class ParameterNodeCanvas extends BaseNodeCanvas {

    public ParameterNodeCanvas() {
        this.setTypeId(NodeTypeEnum.PARAMETER.getValue());
        this.setName("连接参数任务");
    }
}
