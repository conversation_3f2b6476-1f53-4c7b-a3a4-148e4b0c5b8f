package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取多条信息任务节点
 * 对应NodeTypeEnum.FIND_MORE_MESSAGE (1001)
 */
@Getter
@Setter
@DataBean
public class FindMoreMessageNodeCanvas extends BaseNodeCanvas {

    public FindMoreMessageNodeCanvas() {
        this.setTypeId(NodeTypeEnum.FIND_MORE_MESSAGE.getValue());
        this.setName("获取多条信息任务");
    }
}
