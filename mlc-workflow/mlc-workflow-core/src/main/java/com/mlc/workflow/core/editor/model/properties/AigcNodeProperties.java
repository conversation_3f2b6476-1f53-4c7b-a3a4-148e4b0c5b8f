package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * AIGC 任务节点
 * 对应NodeTypeEnum.AIGC (31)
 */
@Getter
@Setter
public class AigcNodeProperties extends BaseNodeProperties {

    public AigcNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.AIGC.getValue());
        this.setName("AIGC 任务");
    }
}
