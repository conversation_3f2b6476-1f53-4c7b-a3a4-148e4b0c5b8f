package com.mlc.workflow.core.editor.structure.traverse;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Stack;
import lombok.Getter;
import lombok.Setter;

/**
 * 遍历上下文
 * 维护遍历过程中的状态信息
 */
@Getter
@Setter
public class TraverseContext {
    
    /**
     * 当前处理的流程节点
     */
    private ProcessNode currentProcessNode;
    
    /**
     * 已访问的节点集合（防止环路）
     */
    private Set<String> visitedNodes = new HashSet<>();
    
    /**
     * 当前遍历路径
     */
    private List<String> currentPath = new ArrayList<>();
    
    /**
     * 遍历深度
     */
    private int depth = 0;
    
    /**
     * 最大遍历深度（防止无限递归）
     */
    private int maxDepth = 100;
    
    /**
     * 是否在子流程中
     */
    private boolean inSubProcess = false;
    
    /**
     * 父流程上下文栈
     */
    private Stack<ProcessNode> processStack = new Stack<>();
    
    /**
     * 自定义属性映射
     */
    private Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 构造函数
     * @param processNode 流程节点
     */
    public TraverseContext(ProcessNode processNode) {
        this.currentProcessNode = processNode;
    }
    
    /**
     * 检查节点是否已访问
     * @param nodeId 节点ID
     * @return 是否已访问
     */
    public boolean isVisited(String nodeId) {
        return visitedNodes.contains(nodeId);
    }
    
    /**
     * 标记节点为已访问
     * @param nodeId 节点ID
     */
    public void markVisited(String nodeId) {
        visitedNodes.add(nodeId);
        currentPath.add(nodeId);
    }
    
    /**
     * 取消访问标记（用于回溯）
     * @param nodeId 节点ID
     */
    public void unmarkVisited(String nodeId) {
        visitedNodes.remove(nodeId);
        currentPath.remove(nodeId);
    }
    
    /**
     * 进入子流程
     * @param subProcessNode 子流程节点
     */
    public void enterSubProcess(ProcessNode subProcessNode) {
        processStack.push(currentProcessNode);
        currentProcessNode = subProcessNode;
        inSubProcess = true;
        depth++;
    }
    
    /**
     * 退出子流程
     */
    public void exitSubProcess() {
        if (!processStack.isEmpty()) {
            currentProcessNode = processStack.pop();
            inSubProcess = !processStack.isEmpty();
            depth--;
        }
    }
    
    /**
     * 检查是否超过最大深度
     * @return 是否超过最大深度
     */
    public boolean isMaxDepthExceeded() {
        return depth >= maxDepth;
    }
    
    /**
     * 获取当前路径的字符串表示
     * @return 路径字符串
     */
    public String getCurrentPathString() {
        return String.join(" -> ", currentPath);
    }
    
    /**
     * 设置自定义属性
     * @param key 属性键
     * @param value 属性值
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }
    
    /**
     * 获取自定义属性
     * @param key 属性键
     * @return 属性值
     */
    public Object getAttribute(String key) {
        return attributes.get(key);
    }
    
    /**
     * 获取自定义属性（带默认值）
     * @param key 属性键
     * @param defaultValue 默认值
     * @param <T> 属性类型
     * @return 属性值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        Object value = attributes.get(key);
        return value != null ? (T) value : defaultValue;
    }
    
    /**
     * 创建子上下文（用于子流程）
     * @param subProcessNode 子流程节点
     * @return 子上下文
     */
    public TraverseContext createSubContext(ProcessNode subProcessNode) {
        TraverseContext subContext = new TraverseContext(subProcessNode);
        subContext.maxDepth = this.maxDepth;
        subContext.depth = this.depth + 1;
        subContext.inSubProcess = true;
        // 复制部分属性
        subContext.attributes.putAll(this.attributes);
        return subContext;
    }
}
