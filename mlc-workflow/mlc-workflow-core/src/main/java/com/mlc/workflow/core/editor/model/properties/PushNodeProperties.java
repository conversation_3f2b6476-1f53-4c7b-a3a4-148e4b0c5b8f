package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 界面推送任务节点
 * 对应NodeTypeEnum.PUSH (17)
 */
@Getter
@Setter
public class PushNodeProperties extends BaseNodeProperties {

    public PushNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.PUSH.getValue());
        this.setName("界面推送任务");
    }
}
