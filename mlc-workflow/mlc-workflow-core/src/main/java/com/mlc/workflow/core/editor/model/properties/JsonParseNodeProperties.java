package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * JSON 解析任务节点
 * 对应NodeTypeEnum.JSON_PARSE (21)
 */
@Getter
@Setter
public class JsonParseNodeProperties extends BaseNodeProperties {

    public JsonParseNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.JSON_PARSE.getValue());
        this.setName("JSON 解析任务");
    }
}
