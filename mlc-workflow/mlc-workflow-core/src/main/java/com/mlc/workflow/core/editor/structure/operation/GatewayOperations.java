package com.mlc.workflow.core.editor.structure.operation;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.ValidationUtils;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 */
@Slf4j
public class GatewayOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final NodeBatchExecutor nodeBatchExecutor;

    public GatewayOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.autoWireStrategy = autoWireStrategy;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }

    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }

    /**
     * 新增网关（默认并行）
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement) {
        return addGateway(atNodeId, placement, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
    }

    /**
     * 新增网关
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement, Integer gatewayType) {
        // 使用统一的参数校验
        ValidationUtils.validateGatewayOperationParams(atNodeId, "atNodeId");
        ValidationUtils.validateExecutorState(nodeBatchExecutor);
        ValidationUtils.validateGatewayType(gatewayType);

        // 从工作副本获取节点
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas atNode = ValidationUtils.requireNodeExists(workingCopy, atNodeId, "插入点");
        IRoutable routableAtNode = ValidationUtils.requireRoutable(atNode, "插入点节点");

        String originalNextId = routableAtNode.getNextId();

        // 创建网关和分支叶子
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName("网关");
        gateway.setGatewayType(gatewayType); // 设置网关类型

        ConditionNodeCanvas leftBranch = new ConditionNodeCanvas();
        leftBranch.setName("分支1");

        ConditionNodeCanvas rightBranch = new ConditionNodeCanvas();
        rightBranch.setName("分支2");

        // 如果是唯一分支网关，为分支叶子设置默认条件
        if (gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0);
            setDefaultConditionForBranch(rightBranch, 1);
        }

        // 设置网关的分支
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        // 设置分支叶子的前驱
        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());

        nodeBatchExecutor.createNode(gateway);
        nodeBatchExecutor.createNode(leftBranch);
        nodeBatchExecutor.createNode(rightBranch);

        // 根据放置策略处理
        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            handleLeftPlacement(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleNoMove(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        }

        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);

        return gateway;
    }

    /**
     * 处理左侧放置策略
     * 根据设计方案：在网关中无论插入网关还是分支始终保持相对最上级网关为nextId=99
     */
    private void handleLeftPlacement(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        if (originalNextId != null && !originalNextId.trim().isEmpty()) {
            if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
                // 原来指向结束，根据设计方案：在网关中保持相对最上级网关为nextId=99
                // 将左分支连接到结束，但通过网关的nextId来实现
                leftBranch.setNextId(""); // 分支内部结束为空
                rightBranch.setNextId(""); // 右分支也为空

                // 网关本身指向结束，保持EndOwner在网关层级
                // 注意：atNode.setNextId(gateway.getId()) 已经在上面设置了，这里直接设置网关为EndOwner
                gateway.setNextId(EndOwnerManager.END_OWNER_ID);

                log.debug("左侧放置：原节点指向结束，网关 {} 成为新的EndOwner载体", gateway.getId());
            } else {
                // 将原有的下游链段移动到左分支
                leftBranch.setNextId(originalNextId);

                // 更新原下游节点的前驱
                BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
                if (originalNext instanceof IRoutable routableNext) {
                    routableNext.setPrveId(leftBranch.getId());
                }

                // 检查原下游节点是否是EndOwner
                String branchTailNext = findBranchTailNext(processNode, originalNextId);
                if (EndOwnerManager.END_OWNER_ID.equals(branchTailNext)) {
                    // 如果原链段的尾部指向结束，需要将EndOwner转移到新网关
                    // 找到原链段中的EndOwner节点并修改其nextId
                    BaseNodeCanvas endOwnerNode = findEndOwnerInChain(processNode, originalNextId);
                    if (endOwnerNode instanceof IRoutable routableEndOwner) {
                        routableEndOwner.setNextId(""); // 原EndOwner不再指向结束
                        nodeBatchExecutor.updateNode(endOwnerNode.getId(), endOwnerNode);
                        log.debug("移除原EndOwner {} 的结束指向", endOwnerNode.getId());
                    }
                    
                    // 新网关成为EndOwner
                    gateway.setNextId(EndOwnerManager.END_OWNER_ID);
                    log.debug("左侧放置：转移EndOwner到新网关 {}", gateway.getId());
                } else {
                    // 网关合流后继续原有流程
                    gateway.setNextId(branchTailNext);
                    log.debug("左侧放置：移动原链段到左分支，网关合流到 {}", branchTailNext);
                }
            }
        } else {
            // 原来没有下游，两个分支都为空
            leftBranch.setNextId("");
            rightBranch.setNextId("");
            gateway.setNextId("");
        }

        // 右分支设为空分支
        rightBranch.setNextId("");
    }

    /**
     * 处理不移动策略
     * 根据设计方案：在网关中保持EndOwner在网关层级
     */
    private void handleNoMove(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        // 处理网关的下游连接
        if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
            // 原来指向结束，根据设计方案：网关层级保持EndOwner
            // 注意：atNode.setNextId(gateway.getId()) 已经在上面设置了，这里直接设置网关为EndOwner
            gateway.setNextId(EndOwnerManager.END_OWNER_ID);
            log.debug("不移动策略：网关 {} 连接到结束", gateway.getId());
        } else {
            // 网关合流后继续原有下游
            gateway.setNextId(originalNextId);

            // 更新原下游节点的前驱
            if (originalNextId != null && !originalNextId.trim().isEmpty()) {
                BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
                if (originalNext instanceof IRoutable routableNext) {
                    routableNext.setPrveId(gateway.getId());
                }
            }

            log.debug("不移动策略：网关 {} 合流到 {}", gateway.getId(), originalNextId);
        }

        // 两个分支都设为空分支（等待后续添加内容）
        leftBranch.setNextId("");
        rightBranch.setNextId("");
    }

    /**
     * 查找分支尾部的下一个节点
     */
    private String findBranchTailNext(ProcessNode processNode, String startNodeId) {
        Set<String> visited = new HashSet<>();
        return findBranchTailNextRecursive(processNode, startNodeId, visited);
    }

    private String findBranchTailNextRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return "";
        }

        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);

        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty() || EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                return nextId;
            }
            return findBranchTailNextRecursive(processNode, nextId, visited);
        }

        return "";
    }

    /**
     * 在链段中查找EndOwner节点
     */
    private BaseNodeCanvas findEndOwnerInChain(ProcessNode processNode, String startNodeId) {
        Set<String> visited = new HashSet<>();
        return findEndOwnerInChainRecursive(processNode, startNodeId, visited);
    }

    private BaseNodeCanvas findEndOwnerInChainRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return null;
        }

        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);

        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                return currentNode; // 找到EndOwner节点
            }
            if (nextId != null && !nextId.trim().isEmpty()) {
                return findEndOwnerInChainRecursive(processNode, nextId, visited);
            }
        }

        return null;
    }

    /**
     * 删除网关
     * @param gatewayId 网关ID
     */
    public void deleteGateway(String gatewayId) {
        if (nodeBatchExecutor == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }

        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }

        // 只剩一条分支，执行扁平化
        String remainingBranchId = flowIds.get(0);
        flattenGateway(gateway, remainingBranchId);

        nodeBatchExecutor.deleteNode(gatewayId);

        log.debug("删除网关 {}，已扁平化", gatewayId);
    }

    /**
     * 扁平化网关（将单分支网关替换为分支链）
     * 根据设计方案：使用Replace操作原语替换网关
     */
    private void flattenGateway(GatewayNodeCanvas gateway, String branchLeafId) {
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();

        // 查找所有指向网关的前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(workingCopy, gateway.getId());
        String gatewayNextId = gateway.getNextId();

        log.debug("扁平化网关 {}，前驱节点数: {}，网关nextId: {}", gateway.getId(), prevNodes.size(), gatewayNextId);

        // 获取分支链
        List<BaseNodeCanvas> branchChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);

        if (branchChain.isEmpty()) {
            // 分支为空，直接连接前驱到网关的下游
            handleEmptyBranchFlatten(workingCopy, prevNodes, gatewayNextId);
        } else {
            // 分支不为空，用分支链替换网关
            handleNonEmptyBranchFlatten(workingCopy, prevNodes, branchChain, gatewayNextId);
        }

        log.debug("网关 {} 扁平化完成", gateway.getId());
    }

    /**
     * 处理空分支的扁平化
     */
    private void handleEmptyBranchFlatten(ProcessNode workingCopy, List<BaseNodeCanvas> prevNodes, String gatewayNextId) {
        // 直接连接前驱到网关的下游
        for (BaseNodeCanvas prevNode : prevNodes) {
            this.connectivityGateway(workingCopy, gatewayNextId, prevNode);
        }
        log.debug("空分支扁平化完成，直接连接前驱到后继");
    }

    /**
     * 连接前驱到网关下游，并更新下游节点的前驱
     * @param workingCopy 工作副本
     * @param gatewayNextId 网关的下游节点ID
     * @param prevNode 前驱节点
     */
    private void connectivityGateway(ProcessNode workingCopy, String gatewayNextId, BaseNodeCanvas prevNode) {
        if (prevNode instanceof IRoutable routablePrev) {
            if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                // 网关指向结束，前驱节点连接到结束
                routablePrev.setNextId(EndOwnerManager.END_OWNER_ID);
            } else {
                routablePrev.setNextId(gatewayNextId);
                // 更新下游节点的前驱
                if (gatewayNextId != null && !gatewayNextId.trim().isEmpty()) {
                    BaseNodeCanvas nextNode = workingCopy.getFlowNodeMap().get(gatewayNextId);
                    if (nextNode instanceof IRoutable routableNext) {
                        routableNext.setPrveId(prevNode.getId());
                        nodeBatchExecutor.updateNode(nextNode.getId(), nextNode);
                    }
                }
            }
            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
        }
    }

    /**
     * 处理非空分支的扁平化
     */
    private void handleNonEmptyBranchFlatten(ProcessNode workingCopy, List<BaseNodeCanvas> prevNodes,
        List<BaseNodeCanvas> branchChain, String gatewayNextId) {
        BaseNodeCanvas branchHead = branchChain.get(0);
        BaseNodeCanvas branchTail = branchChain.get(branchChain.size() - 1);

        // 连接前驱到分支头
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(branchHead.getId());
                nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
            }
        }

        // 更新分支头的前驱
        if (branchHead instanceof IRoutable routableHead && !prevNodes.isEmpty()) {
            routableHead.setPrveId(prevNodes.get(0).getId());
            nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
        }

        // 处理分支尾到网关下游的连接
        this.connectivityGateway(workingCopy, gatewayNextId, branchTail);

        // 更新分支链中的所有节点
        for (BaseNodeCanvas chainNode : branchChain) {
            nodeBatchExecutor.updateNode(chainNode.getId(), chainNode);
        }

        log.debug("非空分支扁平化完成，分支链: {} -> {}", branchHead.getId(), branchTail.getId());
    }

    /**
     * 修改网关类型
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(String gatewayId, Integer toType) {
        if (nodeBatchExecutor == null || gatewayId == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 使用网关语义策略处理类型切换
        GatewaySemanticsStrategy.switchGatewayType(workingCopy, gateway, toType);

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 更新所有被修改的分支节点
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchNode = workingCopy.getFlowNodeMap().get(flowId);
                if (branchNode != null) {
                    nodeBatchExecutor.updateNode(flowId, branchNode);
                    log.debug("更新分支节点: {}", flowId);
                }
            }
        }

        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    /**
     * 为分支设置默认条件
     *
     * @param branch 分支节点
     * @param index 分支索引
     */
    private void setDefaultConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == 2 - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
