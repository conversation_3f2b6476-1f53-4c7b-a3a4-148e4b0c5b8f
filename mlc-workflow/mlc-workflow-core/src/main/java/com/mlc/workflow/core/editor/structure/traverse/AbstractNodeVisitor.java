package com.mlc.workflow.core.editor.structure.traverse;

import com.mlc.workflow.core.editor.model.canvas.ApprovalNode;
import com.mlc.workflow.core.editor.model.canvas.ApprovalProcessNode;
import com.mlc.workflow.core.editor.model.canvas.CcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GetMoreRecordNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.NotifyNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SubProcessNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.SystemNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WriteNode;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;

/**
 * 抽象节点访问者
 * 提供默认的空实现，子类可以选择性重写需要的方法
 */
public abstract class AbstractNodeVisitor implements NodeVisitor {
    
    @Override
    public void visitStartEvent(StartEventNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitGateway(GatewayNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitCondition(ConditionNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitApproval(ApprovalNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitApprovalProcess(ApprovalProcessNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitWrite(WriteNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitCc(CcNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitNotify(NotifyNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitGetMoreRecord(GetMoreRecordNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitSubProcess(SubProcessNodeCanvas node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitSystem(SystemNodeCanvas node, TraverseContext context) {
        // SystemNode 的默认处理
        // 可以在子类中重写以提供特定的处理逻辑
    }
    
    @Override
    public void visitFlowNode(FlowNodeCanvas node, TraverseContext context) {
        // 默认空实现
    }
    
    @Override
    public void startVisit(ProcessNode processNode, TraverseContext context) {
        // 默认空实现
    }
    
    @Override
    public void endVisit(ProcessNode processNode, TraverseContext context) {
        // 默认空实现
    }
}
