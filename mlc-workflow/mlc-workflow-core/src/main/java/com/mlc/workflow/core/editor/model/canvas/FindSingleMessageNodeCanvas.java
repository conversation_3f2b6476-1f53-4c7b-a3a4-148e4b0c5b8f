package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条信息任务节点
 * 对应NodeTypeEnum.FIND_SINGLE_MESSAGE (1000)
 */
@Getter
@Setter
@DataBean
public class FindSingleMessageNodeCanvas extends BaseNodeCanvas {

    public FindSingleMessageNodeCanvas() {
        this.setTypeId(NodeTypeEnum.FIND_SINGLE_MESSAGE.getValue());
        this.setName("获取单条信息任务");
    }
}
