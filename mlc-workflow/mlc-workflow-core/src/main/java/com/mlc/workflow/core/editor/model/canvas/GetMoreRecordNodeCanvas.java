package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.canvas.capability.IExceptional;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 人工节点操作明细(typeId=13)
 * 用于获取更多记录
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class GetMoreRecordNodeCanvas extends FlowNodeCanvas implements IExceptional {
    
    /**
     * 应用类型
     */
    private Integer appType;
    
    /**
     * 操作ID
     */
    private String actionId;
    
    /**
     * 是否执行
     */
    private Boolean execute;

    public GetMoreRecordNodeCanvas() {
        this.setTypeId(NodeTypeEnum.GET_MORE_RECORD.getValue());
        this.setName("人工节点操作明细");
    }
    
    /**
     * 验证异常节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && isExceptional();
    }
}
