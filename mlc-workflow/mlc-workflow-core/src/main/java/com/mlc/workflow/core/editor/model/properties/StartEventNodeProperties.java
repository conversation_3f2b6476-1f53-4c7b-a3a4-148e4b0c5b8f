package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * 起始事件节点 (typeId=0)
 * 工作流的起始节点，触发工作流的执行
 */
@Getter
@Setter
public class StartEventNodeProperties extends BaseNodeProperties {

    public StartEventNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.START.getValue());
        this.setName("发起任务");
    }

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用类型
     */
    private String appType;

    /**
     * 分配字段ID
     */
    private String assignFieldId;

    /**
     * 分配字段ID列表
     */
    private List<String> assignFieldIds;

    /**
     * 操作条件
     */
    private List<String> operateCondition;

    /**
     * 触发器ID
     */
    private String triggerId;

    /**
     * 执行时间
     */
    private String executeTime;

    /**
     * 执行时间类型
     */
    private Integer executeTimeType;

    /**
     * 数字
     */
    private Integer number;

    /**
     * 单位
     */
    private String unit;

    /**
     * 时间
     */
    private String time;



    /**
     * 执行结束时间
     */
    private String executeEndTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 重复类型
     */
    private String repeatType;

    /**
     * 间隔
     */
    private Integer interval;

    /**
     * 频率
     */
    private Integer frequency;

    /**
     * 星期几
     */
    private List<Integer> weekDays;

    /**
     * 配置
     */
    private Map<String, Object> config;

    /**
     * 是否更新
     */
    private Boolean isUpdate;

    /**
     * 控件列表
     */
    private List<Map<String, Object>> controls;

    /**
     * 返回JSON
     */
    private String returnJson;

    /**
     * 返回值列表
     */
    private List<Map<String, Object>> returns;

    /**
     * 流程配置
     */
    private Map<String, Object> processConfig;

    /**
     * 钩子主体
     */
    private String hooksBody;

    /**
     * 字段列表
     */
    private List<Map<String, Object>> fields;

    /**
     * 流程节点映射
     */
    private Map<String, Object> flowNodeMap;

    /**
     * 添加不允许查看
     */
    private Boolean addNotAllowView;

    /**
     * 表单属性
     */
    private Map<String, Object> formProperties;
}
