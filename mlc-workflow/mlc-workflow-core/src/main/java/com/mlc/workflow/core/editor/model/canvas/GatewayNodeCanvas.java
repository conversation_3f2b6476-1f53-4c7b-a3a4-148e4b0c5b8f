package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasBranches;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 分支网关节点 (typeId=1)
 * 用于流程的分支控制
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class GatewayNodeCanvas extends FlowNodeCanvas implements IHasBranches {
    
    /**
     * 网关类型
     * 1 - 并行分支、2 - 唯一分支
     *
     */
    private Integer gatewayType;
    
    /**
     * 分支流ID列表
     */
    private List<String> flowIds = new ArrayList<>();

    public GatewayNodeCanvas() {
        this.setTypeId(NodeTypeEnum.BRANCH.getValue());
        this.setName("分支网关");
        // 设置默认网关类型为并行分支
        this.gatewayType = 1; // GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL
    }

    /**
     * 验证网关节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && 
               gatewayType != null && 
               hasValidBranches();
    }
}
