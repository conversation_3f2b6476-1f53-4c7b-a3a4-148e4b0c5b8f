package com.mlc.workflow.core.editor.model.canvas.capability;

import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;

import java.util.List;

/**
 * 具有条件能力接口
 */
public interface IHasConditions {
    
    /**
     * 获取操作条件列表
     * @return 操作条件列表
     */
    List<List<ConditionGroup>> getOperateCondition();
    
    /**
     * 设置操作条件列表
     * @param operateCondition 操作条件列表
     */
    void setOperateCondition(List<List<ConditionGroup>> operateCondition);
    
    /**
     * 验证条件是否有效
     * @return 是否有效
     */
    default boolean hasValidConditions() {
        List<List<ConditionGroup>> conditions = getOperateCondition();
        return conditions != null && !conditions.isEmpty() &&
               conditions.stream().allMatch(conditionGroup ->
                   conditionGroup != null && !conditionGroup.isEmpty() &&
                   conditionGroup.stream().allMatch(condition ->
                       condition != null && condition.getNodeId() != null && 
                       condition.getFiledId() != null));
    }
}
