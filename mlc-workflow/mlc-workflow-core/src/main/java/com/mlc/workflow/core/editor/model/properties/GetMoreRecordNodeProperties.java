package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取多条记录节点
 * 对应NodeTypeEnum.GET_MORE_RECORD (13)
 */
@Getter
@Setter
public class GetMoreRecordNodeProperties extends BaseNodeProperties {

    public GetMoreRecordNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.GET_MORE_RECORD.getValue());
        this.setName("获取多条记录");
    }
}
