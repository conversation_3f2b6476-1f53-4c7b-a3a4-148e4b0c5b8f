package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasSubProcess;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 审批流程节点 (typeId=26)
 * 包含子流程的审批节点
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class ApprovalProcessNode extends FlowNodeCanvas implements IHasAccounts, IHasSubProcess {
    
    /**
     * 账户列表
     */
    private List<Account> accounts;
    
    /**
     * 表单属性列表
     */
    private List<Object> formProperties;
    
    /**
     * 源应用ID
     */
    private String sourceAppId;
    
    /**
     * 子流程节点
     */
    private ProcessNode processNode;

    public ApprovalProcessNode() {
        this.setName("审批流程节点");
        this.setTypeId(NodeTypeEnum.APPROVAL_PROCESS.getValue());
    }

    /**
     * 验证审批流程节点
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid() && 
               hasValidAccounts() && 
               hasValidSubProcess();
    }
}
