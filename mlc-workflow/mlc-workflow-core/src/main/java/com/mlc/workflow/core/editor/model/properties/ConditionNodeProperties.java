package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 分支项任务节点
 * 对应NodeTypeEnum.BRANCH_ITEM (2)
 */
@Getter
@Setter
public class ConditionNodeProperties extends BaseNodeProperties {

    public ConditionNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.BRANCH_ITEM.getValue());
        this.setName("分支项任务");
    }

    /**
     * 操作条件（表达式）
     */
    private String operateCondition;
}
