package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * 插件任务节点
 * 对应NodeTypeEnum.PLUGIN (32)
 */
@Getter
@Setter
public class PluginNodeProperties extends BaseNodeProperties {

    public PluginNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.PLUGIN.getValue());
        this.setName("插件任务");
    }
}
