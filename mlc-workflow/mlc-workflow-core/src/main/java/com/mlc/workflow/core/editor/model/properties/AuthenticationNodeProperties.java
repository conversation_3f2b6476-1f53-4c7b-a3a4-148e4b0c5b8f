package com.mlc.workflow.core.editor.model.properties;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.AUTHENTICATION (22)
 */
@Getter
@Setter
public class AuthenticationNodeProperties extends BaseNodeProperties {

    public AuthenticationNodeProperties() {
        this.setFlowNodeType(NodeTypeEnum.AUTHENTICATION.getValue());
        this.setName("API 连接与认证任务");
    }
}
