package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.runtime.beans.Account;
import com.mlc.workflow.core.editor.model.canvas.capability.IHasAccounts;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * 起始事件节点 (typeId=0)
 * 工作流的起始节点，触发工作流的执行
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class StartEventNodeCanvas extends FlowNodeCanvas implements IHasAccounts {
    
    /**
     * 应用类型
     */
    private Integer appType;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 分配字段名称列表
     */
    private List<String> assignFieldNames;
    
    /**
     * 触发器ID（用于子流程）
     */
    private String triggerId;
    
    /**
     * 触发器名称（用于子流程）
     */
    private String triggerName;
    
    /**
     * 触发器节点ID（用于子流程）
     */
    private String triggerNodeId;
    
    /**
     * 账户列表（起始节点可能包含账户信息）
     */
    private List<Account> accounts = new ArrayList<>();

    public StartEventNodeCanvas() {
        this.setTypeId(NodeTypeEnum.START.getValue());
        this.setName("起始事件节点");
    }
}
