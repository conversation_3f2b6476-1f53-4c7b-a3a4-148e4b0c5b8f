package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IExceptional;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 工作流节点抽象基类
 * 包含具有流程路由能力的节点的通用字段
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public abstract class FlowNodeCanvas extends BaseNodeCanvas implements IRoutable, IExceptional {
    
    /**
     * 上一个节点ID
     */
    private String prveId = "";
    
    /**
     * 下一个节点ID
     */
    private String nextId = "";

    /**
     * 选择的节点ID
     */
    private String selectNodeId = "";
    
    /**
     * 选择的节点名称
     */
    private String selectNodeName = "";
    
    /**
     * 是否为异常节点
     */
    private Boolean isException = false;
    
    /**
     * 验证流程节点
     * 除了基础验证外，还需要验证路由信息
     */
    @Override
    public boolean isValid() {
        return super.isValid();
    }
}
