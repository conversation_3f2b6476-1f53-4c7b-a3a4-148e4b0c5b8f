package com.mlc.workflow.core.editor.model.canvas;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.api.core.annotations.data.DataBean;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 系统节点 (typeId=100)
 * 用于系统参数和全局变量
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@DataBean
public class SystemNodeCanvas extends BaseNodeCanvas {
    
    public SystemNodeCanvas() {
        this.setTypeId(NodeTypeEnum.SYSTEM.getValue());
        this.setName("系统节点");

    }

    /**
     * 系统节点验证
     * @return 是否有效
     */
    @Override
    public boolean isValid() {
        return super.isValid();
    }
}
