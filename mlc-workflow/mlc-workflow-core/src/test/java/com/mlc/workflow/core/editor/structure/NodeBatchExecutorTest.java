package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.CcNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 验证节点批量执行的正确性和原子性
 */
@Slf4j
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
public class NodeBatchExecutorTest {

    private NodeBatchExecutor nodeBatchExecutor;

    private ProcessNode originalProcessNode;

    @BeforeEach
    void setUp() {
        WorkflowValidator validator = new WorkflowValidator();
        nodeBatchExecutor = new NodeBatchExecutor(validator);


        // 创建测试用的ProcessNode
        originalProcessNode = new ProcessNode();
        originalProcessNode.setFlowNodeMap(new HashMap<>());

        // 添加基本节点
        StartEventNodeCanvas startNode = new StartEventNodeCanvas();
        startNode.setId("start");
        startNode.setName("开始");
        startNode.setNextId("end");

        CcNodeCanvas endNode = new CcNodeCanvas();
        endNode.setId("end");
        endNode.setName("结束");
        endNode.setPrveId("start");

        originalProcessNode.getFlowNodeMap().put("start", startNode);
        originalProcessNode.getFlowNodeMap().put("end", endNode);
    }

    /**
     * 测试开始事务时创建工作副本
     */
    @Test
    void testBeginCreatesWorkingCopy() {
        // 开始事务
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 验证工作副本已创建
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        assertNotNull(workingCopy);
        assertNotSame(originalProcessNode, workingCopy);
        
        // 验证工作副本包含相同的节点
        assertEquals(2, workingCopy.getFlowNodeMap().size());
        assertTrue(workingCopy.getFlowNodeMap().containsKey("start"));
        assertTrue(workingCopy.getFlowNodeMap().containsKey("end"));
    }

    /**
     * 测试创建节点
     */
    @Test
    void testCreateNode() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 创建新节点
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setId("gateway1");
        gateway.setName("网关");
        
        nodeBatchExecutor.createNode(gateway);
        
        // 验证节点已添加到工作副本
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        assertTrue(workingCopy.getFlowNodeMap().containsKey("gateway1"));
        assertEquals(gateway, workingCopy.getFlowNodeMap().get("gateway1"));
        
        // 验证原始ProcessNode未被修改
        assertFalse(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
    }

    /**
     * 测试更新节点
     */
    @Test
    void testUpdateNode() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 更新现有节点
        StartEventNodeCanvas updatedStart = new StartEventNodeCanvas();
        updatedStart.setId("start");
        updatedStart.setName("更新的开始");
        updatedStart.setNextId("gateway1");
        
        nodeBatchExecutor.updateNode("start", updatedStart);
        
        // 验证工作副本中的节点已更新
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        StartEventNodeCanvas nodeInWorkingCopy = (StartEventNodeCanvas) workingCopy.getFlowNodeMap().get("start");
        assertEquals("更新的开始", nodeInWorkingCopy.getName());
        assertEquals("gateway1", nodeInWorkingCopy.getNextId());
        
        // 验证原始ProcessNode未被修改
        StartEventNodeCanvas originalStart = (StartEventNodeCanvas) originalProcessNode.getFlowNodeMap().get("start");
        assertEquals("开始", originalStart.getName());
        assertEquals("end", originalStart.getNextId());
    }

    /**
     * 测试删除节点
     */
    @Test
    void testDeleteNode() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 删除节点
        nodeBatchExecutor.deleteNode("end");
        
        // 验证节点已从工作副本中删除
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        assertFalse(workingCopy.getFlowNodeMap().containsKey("end"));
        assertEquals(1, workingCopy.getFlowNodeMap().size());
        
        // 验证原始ProcessNode未被修改
        assertTrue(originalProcessNode.getFlowNodeMap().containsKey("end"));
        assertEquals(2, originalProcessNode.getFlowNodeMap().size());
    }

    /**
     * 测试提交事务
     * 需要先注释掉 NodeBatchExecutor 中的验证逻辑
     */
    @Test
    void testCommitSuccess() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 进行一些修改
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setId("gateway1");
        gateway.setName("网关");
        nodeBatchExecutor.createNode(gateway);
        
        // 提交
        boolean result = nodeBatchExecutor.commit();
        
        // 验证提交成功
        assertTrue(result);
        
        // 验证修改已应用到原始ProcessNode
        assertTrue(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
        assertEquals(3, originalProcessNode.getFlowNodeMap().size());
    }

    /**
     * 测试回滚功能
     */
    @Test
    void testRollback() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 进行一些修改
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setId("gateway1");
        gateway.setName("网关");
        nodeBatchExecutor.createNode(gateway);
        
        // 验证工作副本包含修改
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        assertTrue(workingCopy.getFlowNodeMap().containsKey("gateway1"));
        
        // 回滚
        nodeBatchExecutor.rollback();
        
        // 验证节点批量执行器已回滚（不能再获取工作副本）
        try {
            nodeBatchExecutor.getWorkingCopy();
            fail("回滚后应该无法获取工作副本");
        } catch (IllegalStateException e) {
            // 预期的异常
        }
        
        // 验证原始ProcessNode未被修改
        assertFalse(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
        assertEquals(2, originalProcessNode.getFlowNodeMap().size());
    }

    /**
     * 测试在单个事务中执行多个操作的原子性
     * 需要先注释掉 NodeBatchExecutor 中的验证逻辑
     */
    @Test
    void testMultipleOperationsInSingleTransaction() {

        nodeBatchExecutor.begin(originalProcessNode);
        
        // 创建节点
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setId("gateway1");
        gateway.setName("网关");
        nodeBatchExecutor.createNode(gateway);
        
        // 更新节点
        StartEventNodeCanvas updatedStart = new StartEventNodeCanvas();
        updatedStart.setId("start");
        updatedStart.setName("更新的开始");
        updatedStart.setNextId("gateway1");
        nodeBatchExecutor.updateNode("start", updatedStart);
        
        // 删除节点
        nodeBatchExecutor.deleteNode("end");
        
        // 提交
        boolean result = nodeBatchExecutor.commit();
        
        // 验证所有操作都已应用
        assertTrue(result);
        assertTrue(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
        assertFalse(originalProcessNode.getFlowNodeMap().containsKey("end"));
        
        StartEventNodeCanvas startNode = (StartEventNodeCanvas) originalProcessNode.getFlowNodeMap().get("start");
        assertEquals("更新的开始", startNode.getName());
        assertEquals("gateway1", startNode.getNextId());
    }

    /**
     * 测试事务隔离性
     * 需要先注释掉 NodeBatchExecutor 中的验证逻辑
     */
    @Test
    void testTransactionIsolation() {
        nodeBatchExecutor.begin(originalProcessNode);
        
        // 在事务中修改
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setId("gateway1");
        gateway.setName("网关");
        nodeBatchExecutor.createNode(gateway);
        
        // 在事务外部检查原始ProcessNode
        assertFalse(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
        assertEquals(2, originalProcessNode.getFlowNodeMap().size());
        
        // 只有在提交后才能看到修改
        nodeBatchExecutor.commit();
        
        assertTrue(originalProcessNode.getFlowNodeMap().containsKey("gateway1"));
        assertEquals(3, originalProcessNode.getFlowNodeMap().size());
    }
}
