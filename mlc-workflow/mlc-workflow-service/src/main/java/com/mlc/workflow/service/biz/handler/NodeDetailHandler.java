package com.mlc.workflow.service.biz.handler;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.service.beans.GetNodeDetailBean;
import io.nop.core.context.IServiceContext;
import java.util.Map;

/**
 * 流程节点详情处理器接口
 * 不同类型的节点通过实现此接口来处理各自的节点详情逻辑
 */
public interface NodeDetailHandler {
    
    /**
     * 判断当前处理器是否支持处理指定类型的节点
     * 
     * @param nodeType 节点类型
     * @return 是否支持
     */
    boolean supports(String nodeType);
    
    /**
     * 获取节点详情
     * 
     * @param request 请求参数
     * @param context 服务上下文
     * @return 节点详情数据
     */
    Map<String, Object> getNodeDetail(GetNodeDetailBean request, IServiceContext context);

    /**
     * 初始化节点详情
     *
     * @param baseNode 请求参数
     * @param context 服务上下文
     */
    void initNodeDetail(BaseNodeCanvas baseNode, IServiceContext context);

    /**
     * 更新节点详情
     *
     * @param request 请求参数
     * @param context 服务上下文
     */
    void updateNodeDetail(GetNodeDetailBean request, IServiceContext context);

    /**
     * 删除节点详情
     *
     * @param request 请求参数
     * @param context 服务上下文
     */
    void deleteNodeDetail(GetNodeDetailBean request, IServiceContext context);
}
