<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <parent>
    <groupId>flowweb-zhongzhi</groupId>
    <artifactId>mlc-ai</artifactId>
    <version>${revision}</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>

  <artifactId>mlc-ai-core</artifactId>

  <dependencies>

    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-starter-model-openai</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.ai</groupId>
      <artifactId>spring-ai-starter-model-deepseek</artifactId>
    </dependency>

    <dependency>
      <groupId>flowweb-zhongzhi</groupId>
      <artifactId>mlc-base-core</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-markdown</artifactId>
    </dependency>

    <dependency>
      <groupId>io.github.entropy-cloud</groupId>
      <artifactId>nop-ai-core</artifactId>
    </dependency>

    <dependency>
      <groupId>org.jgrapht</groupId>
      <artifactId>jgrapht-core</artifactId>
    </dependency>
  </dependencies>
</project>