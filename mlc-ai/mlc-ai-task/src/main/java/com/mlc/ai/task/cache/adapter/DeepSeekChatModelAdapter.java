package com.mlc.ai.task.cache.adapter;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.deepseek.DeepSeekChatOptions;
import org.springframework.ai.deepseek.DeepSeekChatOptions.Builder;

/**
 * DeepSeek ChatModel 适配器
 */
@Slf4j
public class DeepSeekChatModelAdapter implements IChatModelAdapter {

    @Override
    public boolean supports(AiModelTypeEnum modelType, ChatModel chatModel) {
        // 检查是否为 DeepSeek 模型类型
        if (!(chatModel instanceof DeepSeekChatModel)) {
            return false;
        }

        return modelType == AiModelTypeEnum.DEEPSEEK;
    }

    @Override
    public void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config) {
        if (!(chatModel instanceof DeepSeekChatModel)) {
            log.warn("ChatModel 不是 DeepSeekChatModel 类型，跳过配置");
            return;
        }

        Builder optionsBuilder = DeepSeekChatOptions.builder();

        // 配置温度
        if (config.getTemperature() != null) {
            optionsBuilder.temperature(config.getTemperature());
        }

        // 配置最大令牌数
        if (config.getMaxTokens() != null) {
            optionsBuilder.maxTokens(config.getMaxTokens());
        }

        // 配置 topP
        if (config.getTopP() != null) {
            optionsBuilder.topP(config.getTopP());
        }

        // 配置频率惩罚
        if (config.getFrequencyPenalty() != null) {
            optionsBuilder.frequencyPenalty(config.getFrequencyPenalty());
        }

        // 配置存在惩罚
        if (config.getPresencePenalty() != null) {
            optionsBuilder.presencePenalty(config.getPresencePenalty());
        }

        if (optionsBuilder.build() != null) {
            builder.defaultOptions(optionsBuilder.build());
            log.debug("已配置 OpenAI Chat Options: 温度={}, 最大令牌={}",
                      config.getTemperature(), config.getMaxTokens());
        }
    }
}