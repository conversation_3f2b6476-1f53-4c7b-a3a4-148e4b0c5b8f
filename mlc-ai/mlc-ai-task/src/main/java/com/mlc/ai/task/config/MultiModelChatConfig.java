package com.mlc.ai.task.config;

import com.mlc.ai.task.cache.ChatClientCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.ArrayList;
import java.util.List;

/**
 * 多模型聊天配置类
 */
@Slf4j
@Configuration
public class MultiModelChatConfig {

    /**
     * 当只有 OpenAI 模型可用时的配置
     */
    @Bean
    @ConditionalOnBean(value = OpenAiChatModel.class)
    @ConditionalOnProperty(name = "mlc.ai.model.type", havingValue = "openai")
    public ChatClientCacheManager openAiChatClientCacheManager(OpenAiChatModel openAiChatModel) {
        log.info("配置 OpenAI ChatClientCacheManager");
        return new ChatClientCacheManager(openAiChatModel);
    }

    /**
     * 当只有 DeepSeek 模型可用时的配置
     */
    @Bean
    @ConditionalOnBean(value = DeepSeekChatModel.class)
    @ConditionalOnProperty(name = "mlc.ai.model.type", havingValue = "deepseek")
    public ChatClientCacheManager deepseekAiChatClientCacheManager(DeepSeekChatModel deepSeekChatModel) {
        log.info("配置 DeepSeekChatModel ChatClientCacheManager");
        return new ChatClientCacheManager(deepSeekChatModel);
    }

    /**
     * 自动收集所有可用的 ChatModel 实例
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "mlc.ai.model.type", havingValue = "multi", matchIfMissing = true)
    public ChatClientCacheManager multiModelChatClientCacheManager(List<ChatModel> allChatModels) {
        
        List<ChatModel> availableModels = new ArrayList<>();

        for (ChatModel chatModel : allChatModels) {

            // 避免重复添加已经处理的模型
            if (!availableModels.contains(chatModel)) {
                availableModels.add(chatModel);
                log.info("添加 {} 模型到多模型配置", chatModel.getClass().getSimpleName());
            }
        }
        
        if (availableModels.isEmpty()) {
            throw new IllegalStateException("没有可用的 ChatModel 实例，请检查配置");
        }
        
        log.info("配置多模型 ChatClientCacheManager，可用模型: {}", 
            availableModels.stream().map(model -> model.getClass().getSimpleName()).toList());
        
        return new ChatClientCacheManager(availableModels);
    }
}