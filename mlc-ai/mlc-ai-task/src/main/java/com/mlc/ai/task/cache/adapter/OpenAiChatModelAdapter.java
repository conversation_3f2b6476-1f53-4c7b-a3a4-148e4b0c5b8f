package com.mlc.ai.task.cache.adapter;

import com.mlc.ai.core.enums.AiModelTypeEnum;
import com.mlc.ai.task.model.AITaskModel.ChatClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;

/**
 * OpenAI ChatModel 适配器
 */
@Slf4j
public class OpenAiChatModelAdapter implements IChatModelAdapter {

    @Override
    public boolean supports(AiModelTypeEnum modelType, ChatModel chatModel) {
        if (!(chatModel instanceof OpenAiChatModel)) {
            return false;
        }
        
        return modelType == AiModelTypeEnum.OPENAI ||
               modelType == AiModelTypeEnum.DEEPSEEK ||
               modelType == AiModelTypeEnum.QWEN;
    }

    @Override
    public void configureChatOptions(ChatClient.Builder builder, ChatModel chatModel, ChatClientConfig config) {
        if (!(chatModel instanceof OpenAiChatModel)) {
            log.warn("ChatModel 不是 OpenAiChatModel 类型，跳过配置");
            return;
        }

        OpenAiChatOptions options = buildOpenAiChatOptions(config);
        if (options != null) {
            builder.defaultOptions(options);
            log.debug("已配置 OpenAI Chat Options: 温度={}, 最大令牌={}", config.getTemperature(), config.getMaxTokens());
        }
    }

    /**
     * 构建 OpenAI Chat Options
     */
    private OpenAiChatOptions buildOpenAiChatOptions(ChatClientConfig config) {
        OpenAiChatOptions.Builder optionsBuilder = OpenAiChatOptions.builder();
        
        if (config.getTemperature() != null) {
            optionsBuilder.temperature(config.getTemperature());
        }
        if (config.getMaxTokens() != null) {
            optionsBuilder.maxTokens(config.getMaxTokens());
        }
        if (config.getTopP() != null) {
            optionsBuilder.topP(config.getTopP());
        }
        if (config.getFrequencyPenalty() != null) {
            optionsBuilder.frequencyPenalty(config.getFrequencyPenalty());
        }
        if (config.getPresencePenalty() != null) {
            optionsBuilder.presencePenalty(config.getPresencePenalty());
        }
        
        return optionsBuilder.build();
    }
} 